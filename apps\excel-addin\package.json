{"name": "@excella/excel-addin", "version": "1.0.0", "description": "Excella Excel Add-in", "private": true, "main": "src/taskpane/taskpane.js", "scripts": {"build": "webpack --mode=production", "build:dev": "webpack --mode=development", "dev": "webpack serve --mode=development", "start": "office-addin-dev-certs install && npm run dev", "stop": "office-addin-dev-certs uninstall", "validate": "office-addin-manifest validate manifest.xml", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "office-js": "^1.1.89", "@excella/shared": "*", "@excella/ui": "*"}, "devDependencies": {"@types/office-js": "^1.0.374", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "webpack": "^5.96.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.0", "ts-loader": "^9.5.2", "html-webpack-plugin": "^5.6.0", "css-loader": "^7.1.2", "style-loader": "^4.0.0", "file-loader": "^6.2.0", "office-addin-dev-certs": "^1.13.5", "office-addin-manifest": "^1.13.0", "typescript": "^5.8.3"}, "browserslist": ["defaults"]}