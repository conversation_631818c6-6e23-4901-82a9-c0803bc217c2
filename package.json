{"name": "excella-monorepo", "version": "1.0.0", "description": "AI-Powered Excel Add-in - Monorepo", "private": true, "workspaces": ["apps/web-dashboard", "packages/*"], "scripts": {"build": "npm run build --workspaces", "dev": "npm run dev --workspaces", "lint": "npm run lint --workspaces", "test": "npm run test --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules", "install:all": "npm install", "excel-addin:dev": "npm run dev --workspace=apps/excel-addin", "web-dashboard:dev": "npm run dev --workspace=apps/web-dashboard", "excel-addin:build": "npm run build --workspace=apps/excel-addin", "web-dashboard:build": "npm run build --workspace=apps/web-dashboard"}, "devDependencies": {"@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "husky": "^9.0.0", "lint-staged": "^15.0.0", "typescript": "^5.8.3"}, "dependencies": {"framer-motion": "^11.18.2", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.17"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}