{"name": "@excella/web-dashboard", "version": "1.0.0", "description": "Excella Web Dashboard", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.17", "framer-motion": "^11.18.2", "@supabase/supabase-js": "^2.45.0", "@supabase/ssr": "^0.5.0", "@trpc/client": "^11.2.0", "@trpc/server": "^11.2.0", "@trpc/react-query": "^11.2.0", "@tanstack/react-query": "^5.0.0", "zod": "^3.23.0", "@excella/shared": "*", "@excella/ui": "*"}, "devDependencies": {"@types/node": "^22.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.8.3", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "@next/eslint-config-next": "^15.0.0"}}